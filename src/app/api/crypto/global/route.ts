import { NextRequest, NextResponse } from 'next/server';
import { coinGeckoRateLimiter } from '@/lib/rate-limiter';

const COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';

// Simple in-memory cache to reduce API calls
const cache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 300000; // 5 minutes for global data

async function rateLimitedFetch(url: string, options?: RequestInit) {
  await coinGeckoRateLimiter.waitForSlot();
  return fetch(url, options);
}

export async function GET(request: NextRequest) {
  try {
    const cacheKey = 'global';
    
    // Check cache first
    const cached = cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return NextResponse.json(cached.data, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'X-Cache': 'HIT',
        },
      });
    }

    // Build the CoinGecko API URL for global data
    const coinGeckoUrl = `${COINGECKO_API_BASE}/global`;

    // Make the request to CoinGecko with rate limiting
    const response = await rateLimitedFetch(coinGeckoUrl, {
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      // Handle rate limiting specifically
      if (response.status === 429) {
        console.warn('CoinGecko API rate limit exceeded for global data, retrying after delay...');
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
        
        // Retry once
        const retryResponse = await rateLimitedFetch(coinGeckoUrl, {
          headers: {
            'Accept': 'application/json',
          },
        });
        
        if (!retryResponse.ok) {
          throw new Error(`CoinGecko API error after retry: ${retryResponse.status}`);
        }
        
        const retryData = await retryResponse.json();
        
        // Cache the successful response
        cache.set(cacheKey, { data: retryData, timestamp: Date.now() });
        
        return NextResponse.json(retryData, {
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'X-Cache': 'MISS',
          },
        });
      }
      
      throw new Error(`CoinGecko API error: ${response.status}`);
    }

    const data = await response.json();
    
    // Cache the successful response
    cache.set(cacheKey, { data, timestamp: Date.now() });

    // Return the data with CORS headers
    return NextResponse.json(data, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'X-Cache': 'MISS',
      },
    });
  } catch (error) {
    console.error('Error fetching global data:', error);
    
    // If we have cached data, return it even if it's slightly stale
    const cacheKey = 'global';
    const cached = cache.get(cacheKey);
    
    if (cached) {
      console.warn('Returning stale cached global data due to API error');
      return NextResponse.json(cached.data, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'X-Cache': 'STALE',
        },
      });
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch global market data' },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
