module.exports = {

"[project]/.next-internal/server/app/api/crypto/global/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/rate-limiter.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Simple rate limiter utility for CoinGecko API calls
 */ __turbopack_context__.s({
    "coinGeckoRateLimiter": ()=>coinGeckoRateLimiter,
    "default": ()=>__TURBOPACK__default__export__
});
class RateLimiter {
    requests = [];
    lastRequestTime = 0;
    config;
    constructor(config){
        this.config = config;
    }
    async waitForSlot() {
        const now = Date.now();
        // Remove old requests outside the window
        this.requests = this.requests.filter((time)=>now - time < this.config.windowMs);
        // Check if we've exceeded the rate limit
        if (this.requests.length >= this.config.maxRequests) {
            const oldestRequest = Math.min(...this.requests);
            const waitTime = this.config.windowMs - (now - oldestRequest);
            if (waitTime > 0) {
                console.warn(`Rate limit reached, waiting ${waitTime}ms`);
                await new Promise((resolve)=>setTimeout(resolve, waitTime));
                return this.waitForSlot(); // Recursive call to check again
            }
        }
        // Ensure minimum interval between requests
        const timeSinceLastRequest = now - this.lastRequestTime;
        if (timeSinceLastRequest < this.config.minInterval) {
            const waitTime = this.config.minInterval - timeSinceLastRequest;
            await new Promise((resolve)=>setTimeout(resolve, waitTime));
        }
        // Record this request
        this.requests.push(Date.now());
        this.lastRequestTime = Date.now();
    }
}
const coinGeckoRateLimiter = new RateLimiter({
    maxRequests: 10,
    windowMs: 60000,
    minInterval: 1000
});
const __TURBOPACK__default__export__ = RateLimiter;
}),
"[project]/src/app/api/crypto/global/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "OPTIONS": ()=>OPTIONS
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rate$2d$limiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/rate-limiter.ts [app-route] (ecmascript)");
;
;
const COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';
// Simple in-memory cache to reduce API calls
const cache = new Map();
const CACHE_DURATION = 300000; // 5 minutes for global data
async function rateLimitedFetch(url, options) {
    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rate$2d$limiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["coinGeckoRateLimiter"].waitForSlot();
    return fetch(url, options);
}
async function GET(request) {
    try {
        const cacheKey = 'global';
        // Check cache first
        const cached = cache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(cached.data, {
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                    'X-Cache': 'HIT'
                }
            });
        }
        // Build the CoinGecko API URL for global data
        const coinGeckoUrl = `${COINGECKO_API_BASE}/global`;
        // Make the request to CoinGecko with rate limiting
        const response = await rateLimitedFetch(coinGeckoUrl, {
            headers: {
                'Accept': 'application/json'
            }
        });
        if (!response.ok) {
            // Handle rate limiting specifically
            if (response.status === 429) {
                console.warn('CoinGecko API rate limit exceeded for global data, retrying after delay...');
                await new Promise((resolve)=>setTimeout(resolve, 5000)); // Wait 5 seconds
                // Retry once
                const retryResponse = await rateLimitedFetch(coinGeckoUrl, {
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                if (!retryResponse.ok) {
                    throw new Error(`CoinGecko API error after retry: ${retryResponse.status}`);
                }
                const retryData = await retryResponse.json();
                // Cache the successful response
                cache.set(cacheKey, {
                    data: retryData,
                    timestamp: Date.now()
                });
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(retryData, {
                    headers: {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                        'X-Cache': 'MISS'
                    }
                });
            }
            throw new Error(`CoinGecko API error: ${response.status}`);
        }
        const data = await response.json();
        // Cache the successful response
        cache.set(cacheKey, {
            data,
            timestamp: Date.now()
        });
        // Return the data with CORS headers
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(data, {
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                'X-Cache': 'MISS'
            }
        });
    } catch (error) {
        console.error('Error fetching global data:', error);
        // If we have cached data, return it even if it's slightly stale
        const cacheKey = 'global';
        const cached = cache.get(cacheKey);
        if (cached) {
            console.warn('Returning stale cached global data due to API error');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(cached.data, {
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                    'X-Cache': 'STALE'
                }
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch global market data'
        }, {
            status: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            }
        });
    }
}
async function OPTIONS() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
    });
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__2021ad75._.js.map