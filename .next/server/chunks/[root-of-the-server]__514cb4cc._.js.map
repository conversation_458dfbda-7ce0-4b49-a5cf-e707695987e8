{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/lib/rate-limiter.ts"], "sourcesContent": ["/**\n * Simple rate limiter utility for CoinGecko API calls\n */\n\ninterface RateLimiterConfig {\n  maxRequests: number;\n  windowMs: number;\n  minInterval: number;\n}\n\nclass RateLimiter {\n  private requests: number[] = [];\n  private lastRequestTime = 0;\n  private config: RateLimiterConfig;\n\n  constructor(config: RateLimiterConfig) {\n    this.config = config;\n  }\n\n  async waitForSlot(): Promise<void> {\n    const now = Date.now();\n    \n    // Remove old requests outside the window\n    this.requests = this.requests.filter(time => now - time < this.config.windowMs);\n    \n    // Check if we've exceeded the rate limit\n    if (this.requests.length >= this.config.maxRequests) {\n      const oldestRequest = Math.min(...this.requests);\n      const waitTime = this.config.windowMs - (now - oldestRequest);\n      if (waitTime > 0) {\n        console.warn(`Rate limit reached, waiting ${waitTime}ms`);\n        await new Promise(resolve => setTimeout(resolve, waitTime));\n        return this.waitForSlot(); // Recursive call to check again\n      }\n    }\n    \n    // Ensure minimum interval between requests\n    const timeSinceLastRequest = now - this.lastRequestTime;\n    if (timeSinceLastRequest < this.config.minInterval) {\n      const waitTime = this.config.minInterval - timeSinceLastRequest;\n      await new Promise(resolve => setTimeout(resolve, waitTime));\n    }\n    \n    // Record this request\n    this.requests.push(Date.now());\n    this.lastRequestTime = Date.now();\n  }\n}\n\n// CoinGecko free tier limits: 10-50 calls/minute\nexport const coinGeckoRateLimiter = new RateLimiter({\n  maxRequests: 10, // Conservative limit\n  windowMs: 60000, // 1 minute\n  minInterval: 1000, // 1 second between requests\n});\n\nexport default RateLimiter;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAQD,MAAM;IACI,WAAqB,EAAE,CAAC;IACxB,kBAAkB,EAAE;IACpB,OAA0B;IAElC,YAAY,MAAyB,CAAE;QACrC,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,MAAM,cAA6B;QACjC,MAAM,MAAM,KAAK,GAAG;QAEpB,yCAAyC;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,OAAQ,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ;QAE9E,yCAAyC;QACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACnD,MAAM,gBAAgB,KAAK,GAAG,IAAI,IAAI,CAAC,QAAQ;YAC/C,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,MAAM,aAAa;YAC5D,IAAI,WAAW,GAAG;gBAChB,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,SAAS,EAAE,CAAC;gBACxD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,OAAO,IAAI,CAAC,WAAW,IAAI,gCAAgC;YAC7D;QACF;QAEA,2CAA2C;QAC3C,MAAM,uBAAuB,MAAM,IAAI,CAAC,eAAe;QACvD,IAAI,uBAAuB,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAClD,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG;YAC3C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;QAEA,sBAAsB;QACtB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG;QAC3B,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG;IACjC;AACF;AAGO,MAAM,uBAAuB,IAAI,YAAY;IAClD,aAAa;IACb,UAAU;IACV,aAAa;AACf;uCAEe", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/app/api/crypto/search/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { coinGeckoRateLimiter } from '@/lib/rate-limiter';\n\nconst COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';\n\n// Simple in-memory cache to reduce API calls\nconst cache = new Map<string, { data: any; timestamp: number }>();\nconst CACHE_DURATION = 300000; // 5 minutes for search results\n\nasync function rateLimitedFetch(url: string, options?: RequestInit) {\n  await coinGeckoRateLimiter.waitForSlot();\n  return fetch(url, options);\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const query = searchParams.get('query');\n\n    if (!query) {\n      return NextResponse.json(\n        { error: 'Query parameter is required' },\n        { status: 400 }\n      );\n    }\n\n    // Create cache key\n    const cacheKey = `search-${query.toLowerCase()}`;\n\n    // Check cache first\n    const cached = cache.get(cacheKey);\n    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n      return NextResponse.json(cached.data, {\n        headers: {\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n          'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n          'X-Cache': 'HIT',\n        },\n      });\n    }\n\n    // Build the CoinGecko API URL\n    const coinGeckoUrl = new URL(`${COINGECKO_API_BASE}/search`);\n    coinGeckoUrl.searchParams.set('query', query);\n\n    // Make the request to CoinGecko with rate limiting\n    const response = await rateLimitedFetch(coinGeckoUrl.toString(), {\n      headers: {\n        'Accept': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      // Handle rate limiting specifically\n      if (response.status === 429) {\n        console.warn(`CoinGecko API rate limit exceeded for search \"${query}\", retrying after delay...`);\n        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds\n\n        // Retry once\n        const retryResponse = await rateLimitedFetch(coinGeckoUrl.toString(), {\n          headers: {\n            'Accept': 'application/json',\n          },\n        });\n\n        if (!retryResponse.ok) {\n          throw new Error(`CoinGecko API error after retry: ${retryResponse.status}`);\n        }\n\n        const retryData = await retryResponse.json();\n\n        // Cache the successful response\n        cache.set(cacheKey, { data: retryData, timestamp: Date.now() });\n\n        return NextResponse.json(retryData, {\n          headers: {\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n            'X-Cache': 'MISS',\n          },\n        });\n      }\n\n      throw new Error(`CoinGecko API error: ${response.status}`);\n    }\n\n    const data = await response.json();\n\n    // Cache the successful response\n    cache.set(cacheKey, { data, timestamp: Date.now() });\n\n    // Return the data with CORS headers\n    return NextResponse.json(data, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n        'X-Cache': 'MISS',\n      },\n    });\n  } catch (error) {\n    console.error('Error searching cryptocurrencies:', error);\n\n    // If we have cached data, return it even if it's slightly stale\n    const query = request.nextUrl.searchParams.get('query');\n    if (query) {\n      const cacheKey = `search-${query.toLowerCase()}`;\n      const cached = cache.get(cacheKey);\n\n      if (cached) {\n        console.warn(`Returning stale cached data for search \"${query}\" due to API error`);\n        return NextResponse.json(cached.data, {\n          headers: {\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n            'X-Cache': 'STALE',\n          },\n        });\n      }\n    }\n\n    return NextResponse.json(\n      { error: 'Failed to search cryptocurrencies' },\n      {\n        status: 500,\n        headers: {\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n          'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n        },\n      }\n    );\n  }\n}\n\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,qBAAqB;AAE3B,6CAA6C;AAC7C,MAAM,QAAQ,IAAI;AAClB,MAAM,iBAAiB,QAAQ,+BAA+B;AAE9D,eAAe,iBAAiB,GAAW,EAAE,OAAqB;IAChE,MAAM,+HAAA,CAAA,uBAAoB,CAAC,WAAW;IACtC,OAAO,MAAM,KAAK;AACpB;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC;QAE/B,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,mBAAmB;QACnB,MAAM,WAAW,CAAC,OAAO,EAAE,MAAM,WAAW,IAAI;QAEhD,oBAAoB;QACpB,MAAM,SAAS,MAAM,GAAG,CAAC;QACzB,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,gBAAgB;YAC5D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE;gBACpC,SAAS;oBACP,+BAA+B;oBAC/B,gCAAgC;oBAChC,gCAAgC;oBAChC,WAAW;gBACb;YACF;QACF;QAEA,8BAA8B;QAC9B,MAAM,eAAe,IAAI,IAAI,GAAG,mBAAmB,OAAO,CAAC;QAC3D,aAAa,YAAY,CAAC,GAAG,CAAC,SAAS;QAEvC,mDAAmD;QACnD,MAAM,WAAW,MAAM,iBAAiB,aAAa,QAAQ,IAAI;YAC/D,SAAS;gBACP,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,oCAAoC;YACpC,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,QAAQ,IAAI,CAAC,CAAC,8CAA8C,EAAE,MAAM,0BAA0B,CAAC;gBAC/F,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,iBAAiB;gBAE1E,aAAa;gBACb,MAAM,gBAAgB,MAAM,iBAAiB,aAAa,QAAQ,IAAI;oBACpE,SAAS;wBACP,UAAU;oBACZ;gBACF;gBAEA,IAAI,CAAC,cAAc,EAAE,EAAE;oBACrB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,cAAc,MAAM,EAAE;gBAC5E;gBAEA,MAAM,YAAY,MAAM,cAAc,IAAI;gBAE1C,gCAAgC;gBAChC,MAAM,GAAG,CAAC,UAAU;oBAAE,MAAM;oBAAW,WAAW,KAAK,GAAG;gBAAG;gBAE7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;oBAClC,SAAS;wBACP,+BAA+B;wBAC/B,gCAAgC;wBAChC,gCAAgC;wBAChC,WAAW;oBACb;gBACF;YACF;YAEA,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,MAAM,EAAE;QAC3D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,gCAAgC;QAChC,MAAM,GAAG,CAAC,UAAU;YAAE;YAAM,WAAW,KAAK,GAAG;QAAG;QAElD,oCAAoC;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAC7B,SAAS;gBACP,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;gBAChC,WAAW;YACb;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QAEnD,gEAAgE;QAChE,MAAM,QAAQ,QAAQ,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC;QAC/C,IAAI,OAAO;YACT,MAAM,WAAW,CAAC,OAAO,EAAE,MAAM,WAAW,IAAI;YAChD,MAAM,SAAS,MAAM,GAAG,CAAC;YAEzB,IAAI,QAAQ;gBACV,QAAQ,IAAI,CAAC,CAAC,wCAAwC,EAAE,MAAM,kBAAkB,CAAC;gBACjF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE;oBACpC,SAAS;wBACP,+BAA+B;wBAC/B,gCAAgC;wBAChC,gCAAgC;wBAChC,WAAW;oBACb;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAoC,GAC7C;YACE,QAAQ;YACR,SAAS;gBACP,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;YAClC;QACF;IAEJ;AACF;AAEO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}